2025-07-25 13:17:32.792 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-25 13:17:32.792 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-25 13:17:34.446 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-25 13:17:34.446 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-25 13:17:34.460 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-25 13:17:35.109 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-25 13:17:35.781 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-25 13:17:35.782 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-25 13:17:57.103 | ef86060188314ca49b0d4e2913597e37 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为3192345f-1d45-41d8-a5a3-0fe1fabe69ab的会话获取图片验证码成功
2025-07-25 13:17:59.249 | 2eefa2d4d0614bfb9cb4a057268b8c51 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-25 13:17:59.495 | d5937b4565c445e8bf0b75b5ace5bd4d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-25 13:18:00.000 | 76a46bdbeb8646aaaa2cf0f0f5bceec9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-25 13:18:00.528 | adc16c69ffc84885b6d628063d95e984 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-25 13:18:00.593 | adc16c69ffc84885b6d628063d95e984 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-25 13:18:00.593 | adc16c69ffc84885b6d628063d95e984 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-25 13:18:00.823 | 1a12129fb90b4411ba704f34cfef36bf | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-25 13:18:00.853 | 1a12129fb90b4411ba704f34cfef36bf | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-25 13:18:00.854 | 1a12129fb90b4411ba704f34cfef36bf | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-25 13:18:00.854 | 1a12129fb90b4411ba704f34cfef36bf | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-25 13:18:01.007 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-25 13:18:01.007 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-25 13:18:01.008 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-25 13:18:01.008 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-25 00:00:00 到 2025-07-25 23:59:59.999999 ===
2025-07-25 13:18:01.008 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-25 13:18:01.010 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-25 13:18:01.046 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-25 13:18:01.231 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-25 13:18:01.494 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-25 13:18:01.495 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 25, 13, 18, 1, 459283), 'package_name': '企业版', 'is_expired': True} ===
2025-07-25 13:18:01.530 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-25 13:18:01.531 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-25 13:18:01.565 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-25 13:18:01.566 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-25 13:18:01.566 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 13:18:01.566 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 13:18:01.567 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-25 13:18:01.567 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 13:18:01.568 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-25 13:18:01.568 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-25 13:18:01.568 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-25 13:18:01.569 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-25 13:18:01.569 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 13:18:01.570 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-25 13:18:01.570 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x000001B3085768B0> ===
2025-07-25 13:18:03.307 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 25, 13, 17, 59), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-25 13:18:03.308 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-25 13:18:03.308 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-25 13:18:03.308 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-25 13:18:03.309 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-25 13:18:03.309 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-25 13:18:03.310 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-25 13:18:03.310 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-25 13:18:03.311 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-25 13:18:03.311 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-25 13:18:03.311 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-25 13:18:03.312 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-25 13:18:03.312 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-25 13:18:03.315 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-25 13:18:03.316 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-25 13:18:03.373 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-25 13:18:03.373 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-25 13:18:03.374 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-25 13:18:03.374 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-25 13:18:03.374 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-25 13:27:07.923 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 25, 13, 17, 59), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-25 13:27:07.924 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-25 13:27:07.924 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-25 13:27:07.924 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-25 13:27:07.924 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-25 13:27:07.925 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-25 13:27:07.925 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-25 13:27:07.925 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-25 13:27:07.925 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-25 13:27:07.926 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-25 13:27:07.926 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-25 13:27:07.926 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-25 13:27:07.927 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-25 13:27:07.928 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-25 13:27:07.929 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-25 13:27:08.002 | ce3ceaa1356247e0823407effa641ce1 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-25 13:27:08.003 | ce3ceaa1356247e0823407effa641ce1 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-25 13:27:08.003 | ce3ceaa1356247e0823407effa641ce1 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-25 13:27:08.003 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-25 13:27:08.003 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
